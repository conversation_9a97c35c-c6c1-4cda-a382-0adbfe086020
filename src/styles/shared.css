/* 统一的UI样式系统 */

/* 按钮样式 */
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: none;
}

.btn-secondary {
  @apply inline-flex items-center px-4 py-2 border border-slate-300 dark:border-slate-600 text-sm font-medium rounded-lg shadow-sm text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  @apply bg-slate-50 dark:bg-slate-700 border-slate-400 dark:border-slate-500;
  transform: translateY(-1px);
}

.btn-secondary-sm {
  @apply inline-flex items-center px-3 py-1.5 border border-slate-300 dark:border-slate-600 text-xs font-medium rounded-md shadow-sm text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800;
  transition: all 0.2s ease;
}

.btn-secondary-sm:hover {
  @apply bg-slate-50 dark:bg-slate-700 border-slate-400 dark:border-slate-500;
  transform: translateY(-1px);
}

.btn-icon {
  @apply p-2 rounded-full text-slate-400 hover:text-slate-500 hover:bg-slate-100 dark:text-slate-500 dark:hover:text-slate-400 dark:hover:bg-slate-700;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  transform: scale(1.05);
}

/* 表单样式 */
.form-input {
  @apply block w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg shadow-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 placeholder-slate-400 dark:placeholder-slate-500;
  transition: all 0.2s ease;
}

.form-input:focus {
  @apply ring-2 ring-blue-500 dark:ring-blue-400 border-blue-500 dark:border-blue-400 outline-none;
}

.form-label {
  @apply block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2;
}

/* 卡片样式 */
.card {
  @apply bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm;
  transition: all 0.2s ease;
}

.card:hover {
  @apply shadow-md;
  transform: translateY(-1px);
}

.card-header {
  @apply px-6 py-4 border-b border-slate-200 dark:border-slate-700;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-slate-200 dark:border-slate-700;
}

/* 统计卡片 */
.stat-card {
  @apply bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-4 text-center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  @apply shadow-md border-slate-300 dark:border-slate-600;
  transform: translateY(-1px);
}

.stat-value {
  @apply text-2xl font-bold text-slate-900 dark:text-slate-100;
}

.stat-label {
  @apply text-sm text-slate-500 dark:text-slate-400 mt-1;
}

/* 页面布局 */
.page-container {
  @apply h-screen flex flex-col bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200;
}

.page-header {
  @apply flex-shrink-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-lg border-b border-slate-200 dark:border-slate-700 z-20;
}

.page-header-content {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.page-header-inner {
  @apply flex items-center justify-between h-16;
}

.page-title {
  @apply text-xl font-bold truncate;
}

.page-subtitle {
  @apply text-sm text-slate-500 dark:text-slate-400 hidden md:block truncate;
}

/* 侧边栏 */
.sidebar {
  @apply flex-shrink-0 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 overflow-y-auto;
  transition: all 0.3s ease;
}

.sidebar-content {
  @apply p-6 space-y-6;
}

/* 主内容区域 */
.main-content {
  @apply flex-1 flex flex-col overflow-hidden;
}

/* 消息气泡 */
.message-bubble {
  @apply inline-block p-3 rounded-lg max-w-[80%];
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
}

.message-bubble.user {
  @apply bg-blue-500 text-white;
}

.message-bubble.system {
  @apply bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200;
}

.message-bubble.assistant {
  @apply bg-gradient-to-r from-purple-500 to-blue-500 text-white;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin h-5 w-5;
}

/* 滑块样式 */
.slider {
  @apply w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer;
}

.slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-500 rounded-full cursor-pointer;
}

.slider::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-500 rounded-full cursor-pointer border-0;
}

/* 响应式工具类 */
.responsive-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

.responsive-flex {
  @apply flex flex-col sm:flex-row gap-4;
}

/* 动画工具类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    @apply bg-slate-800/50 backdrop-blur-sm;
  }
}

.glass-effect {
  @apply bg-white/50 backdrop-blur-sm;
}
