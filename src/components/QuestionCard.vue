<script setup lang="ts">
import { ref } from 'vue'
import type { Question } from '../utils/questionGenerator'
import { renderMarkdown } from '../utils/markdownRenderer'

interface Props {
  question: Question
}

const props = defineProps<Props>()

const isFavorited = ref(false)
const isHovered = ref(false)
const mouseX = ref(0)
const mouseY = ref(0)

const handleMouseMove = (event: MouseEvent) => {
  const card = event.currentTarget as HTMLElement
  const rect = card.getBoundingClientRect()
  mouseX.value = event.clientX - rect.left
  mouseY.value = event.clientY - rect.top
}

const categoryStyles = {
  critical: 'bg-gradient-to-r from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700/50',
  creative: 'bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-900/30 dark:to-indigo-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700/50',
  logical: 'bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700/50',
  system: 'bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700/50',
  ethical: 'bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700/50',
  practical: 'bg-gradient-to-r from-teal-100 to-cyan-100 dark:from-teal-900/30 dark:to-cyan-900/30 text-teal-700 dark:text-teal-300 border-teal-200 dark:border-teal-700/50'
}

const categoryLabels = {
  critical: '批判性思维',
  creative: '创意思维',
  logical: '逻辑分析',
  system: '系统思维',
  ethical: '伦理思考',
  practical: '实践应用'
}

const difficultyStyles = {
  beginner: 'bg-gradient-to-r from-green-100 to-lime-100 dark:from-green-900/30 dark:to-lime-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700/50',
  intermediate: 'bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700/50',
  advanced: 'bg-gradient-to-r from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700/50'
}

const difficultyLabels = {
  beginner: '入门',
  intermediate: '中级',
  advanced: '高级'
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
}

const startSystemicAnalysis = () => {
  // 打开系统分析页面，但不自动开始分析
  const analysisUrl = `/analysis?topic=${encodeURIComponent(props.question.question)}`
  window.open(analysisUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}

const startRoundtableMeeting = () => {
  // 打开圆桌会议页面，传递话题和思考提示
  const hintsParam = encodeURIComponent(JSON.stringify(props.question.hints))
  const roundtableUrl = `/roundtable?topic=${encodeURIComponent(props.question.question)}&hints=${hintsParam}`
  window.open(roundtableUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes')
}


</script>

<template>
  <div
    class="question-card bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 transition-all duration-300 hover:shadow-xl hover:shadow-slate-200/50 dark:hover:shadow-slate-900/50 hover:-translate-y-1 hover:border-slate-300 dark:hover:border-slate-600"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @mousemove="handleMouseMove"
  >
    <div>
      <!-- 卡片头部 -->
      <div class="flex items-start justify-between mb-4">
        <div class="flex flex-wrap gap-2 flex-1 mr-3">
          <span :class="['px-3 py-1 rounded-lg text-sm font-medium border', categoryStyles[question.category]]">
            {{ categoryLabels[question.category] }}
          </span>
          <span :class="['px-3 py-1 rounded-lg text-sm font-medium border', difficultyStyles[question.difficulty]]">
            {{ difficultyLabels[question.difficulty] }}
          </span>
        </div>
        <button
          @click="toggleFavorite"
          :class="[
            'p-2 rounded-lg transition-colors duration-200 flex-shrink-0',
            isFavorited
              ? 'text-slate-900 dark:text-slate-100 bg-slate-100 dark:bg-slate-700'
              : 'text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800'
          ]"
        >
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              :fill-rule="isFavorited ? 'evenodd' : 'nonzero'"
              d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
              :clip-rule="isFavorited ? 'evenodd' : 'nonzero'"
            />
          </svg>
        </button>
      </div>

      <!-- 问题内容 -->
      <div class="mb-6">
        <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-3 leading-tight">
          {{ question.question }}
        </h4>
        <p class="text-slate-600 dark:text-slate-400 leading-relaxed"
          v-html="renderMarkdown(question.description)">
        </p>
      </div>

      <!-- 提示信息 -->
      <div v-if="question.hints.length > 0" class="mb-6">
        <h5 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3 flex items-center">
          <svg class="h-4 w-4 mr-2 text-slate-500 dark:text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          思考提示
        </h5>
        <ul class="space-y-2">
          <li
            v-for="hint in question.hints"
            :key="hint"
            class="text-sm text-slate-600 dark:text-slate-400 flex items-start"
          >
            <span class="text-slate-500 dark:text-slate-400 mr-2 mt-0.5">•</span>
            <span class="flex-1" v-html="renderMarkdown(hint)"></span>
          </li>
        </ul>
      </div>

      <!-- 卡片底部 -->
      <div class="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
        <div class="flex items-center text-sm text-slate-500 dark:text-slate-400">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ question.estimatedTime }}
        </div>
        <div class="flex items-center justify-end space-x-3">
          <button
            @click="startSystemicAnalysis"
            class="ux-action-btn ux-primary group"
            title="深度系统分析"
          >
            <svg class="h-4 w-4 mr-2 transition-transform group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 712-2h2a2 2 0 712 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>系统分析</span>
          </button>
          <button
            @click="startRoundtableMeeting"
            class="ux-action-btn ux-secondary group"
            title="多角度圆桌讨论"
          >
            <svg class="h-4 w-4 mr-2 transition-transform group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span>圆桌会议</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 优化后的卡片样式 - 减少复杂动画 */
.question-card {
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.question-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* UX优化的按钮样式系统 */
.ux-action-btn {
  @apply inline-flex items-center px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-300 ease-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply transform hover:scale-105 active:scale-95;
  @apply shadow-sm hover:shadow-md;
}

.ux-primary {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white;
  @apply hover:from-blue-700 hover:to-indigo-700;
  @apply focus:ring-blue-500;
  @apply border border-transparent;
}

.ux-secondary {
  @apply bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300;
  @apply border border-slate-300 dark:border-slate-600;
  @apply hover:bg-slate-50 dark:hover:bg-slate-700;
  @apply focus:ring-slate-500;
}

.ux-action-btn:disabled {
  @apply opacity-50 cursor-not-allowed transform-none;
}

/* 简化原有按钮样式 */
.simple-btn {
  transition: all 0.2s ease;
}

.simple-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 简化的标签样式 */
.question-card span[class*="bg-gradient"] {
  transition: transform 0.2s ease;
}

.question-card:hover span[class*="bg-gradient"] {
  transform: translateY(-1px);
}

/* 简化的收藏按钮动画 */
.question-card button[class*="p-2"] {
  transition: transform 0.2s ease;
}

.question-card button[class*="p-2"]:hover {
  transform: scale(1.05);
}
</style>