{"name": "thinking-trainer-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.2.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "markdown-it": "^14.1.0", "tailwind-scrollbar": "^3.0.0", "vue": "^3.4.38", "vue-router": "^4.2.5"}, "devDependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/cli": "^2.6.2", "@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}